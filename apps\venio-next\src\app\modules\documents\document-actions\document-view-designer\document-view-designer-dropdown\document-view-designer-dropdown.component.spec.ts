import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerDropdownComponent } from './document-view-designer-dropdown.component'
import { provideMockStore } from '@ngrx/store/testing'
import {
  FieldFacade,
  SearchFacade,
  StartupsFacade,
  ViewModel,
  ViewFacade as ReviewViewFacade,
} from '@venio/data-access/review'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  provideRouter,
  ActivatedRoute,
  ActivatedRouteSnapshot,
} from '@angular/router'
import { UserFacade, DocumentViewFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { DialogService } from '@progress/kendo-angular-dialog'
import { ViewContainerRef, signal } from '@angular/core'
import { CommonActionTypes } from '@venio/shared/models/constants'

// Type-safe mock implementations
const mockDialogService = {
  open: jest.fn(),
} satisfies Partial<DialogService>

const mockViewContainerRef = {
  clear: jest.fn(),
  length: 0,
} satisfies Partial<ViewContainerRef>

const mockDocumentViewFacade = {
  fetchDocumentViewList: jest.fn(),
  resetDocumentViewState: jest.fn(),
  storeSelectedDocumentView: jest.fn(),
  fetchViewByViewId: jest.fn(),
  selectDocumentViewListSuccessResponse$: of(null),
  selectDocumentViewListErrorResponse$: of(null),
} satisfies Partial<DocumentViewFacade>

const mockBreadcrumbService = {
  setConditionChecked: jest.fn(),
} satisfies Partial<BreadcrumbService>

const mockReviewViewFacade = {
  saveUserDefaultView: jest.fn(),
  isViewManuallyChanged: signal(false),
  storeSelectedViewDefaultExpression: jest.fn(),
  fetchUserDefaultView: jest.fn(),
  resetView: jest.fn(),
  setViewActionType: jest.fn(),
  selectUserDefaultView$: of(null),
} satisfies Partial<ReviewViewFacade>

const mockSearchFacade = {
  resetSearchInputControls: jest.fn(),
  resetSearchState: jest.fn(),
} satisfies Partial<SearchFacade>

const mockFieldFacade = {
  fetchAllCustomFields: jest.fn(),
} satisfies Partial<FieldFacade>

// Create a proper ActivatedRouteSnapshot mock
const mockActivatedRouteSnapshot = {
  queryParams: {
    projectId: 123,
  },
  url: [],
  params: {},
  fragment: null,
  data: {},
  outlet: '',
  component: null,
  routeConfig: null,
  root: null as any,
  parent: null,
  firstChild: null,
  children: [],
  pathFromRoot: [],
  paramMap: of(null),
  queryParamMap: of(null),
} as unknown as ActivatedRouteSnapshot

const mockActivatedRoute = {
  snapshot: mockActivatedRouteSnapshot,
} satisfies Partial<ActivatedRoute>

describe('DocumentViewDesignerDropdownComponent', () => {
  let component: DocumentViewDesignerDropdownComponent
  let fixture: ComponentFixture<DocumentViewDesignerDropdownComponent>
  let mockStartupFacade: any
  let mockUserFacade: any

  beforeEach(async () => {
    // Mock the startup facade to control view management rights
    mockStartupFacade = {
      hasGlobalRight$: jest.fn((userRight: any) => of(true)),
    }

    // Mock user facade for external user testing
    mockUserFacade = {
      selectCurrentUserDetails$: of({
        userId: 1,
        fullName: 'Test User',
        userName: 'testuser',
        address: '',
        email: '<EMAIL>',
        phone: '',
        mobile: '',
        fax: '',
        globalRoleId: 1,
        isUserLocked: false,
        isUserDeactivated: false,
        failedLoginAttempts: 0,
        userLockValidUpto: new Date(),
        userLockType: '',
        forceUserToChangePassword: false,
        hasDesktopAccess: true,
        hasWebECAAccess: true,
        hasReviewAccess: true,
        hasTouchAccess: true,
        isADUser: false,
        adUserSID: '',
        adUserGUID: '',
        hasOnDemandAccess: true,
        clientId: 1,
        clientName: 'Test Client',
        showNotification: true,
        isUserApproved: true,
        createdByName: 'Admin',
        createdDate: new Date(),
        userRole: 'internal',
        globalRoleName: 'User',
        isUserAdmin: false,
        userCaseAssignmentModel: '',
        eulaAcceptance: true,
        userlayoutId: 1,
        passwordExpired: false,
        activeSessionId: 1,
        disablePasswordReset: false,
      }),
    }

    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerDropdownComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        provideMockStore({}),
        { provide: DialogService, useValue: mockDialogService },
        { provide: DocumentViewFacade, useValue: mockDocumentViewFacade },
        { provide: BreadcrumbFacade, useValue: {} },
        { provide: BreadcrumbService, useValue: mockBreadcrumbService },
        { provide: ReviewViewFacade, useValue: mockReviewViewFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: FieldFacade, useValue: mockFieldFacade },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        {
          provide: UserFacade,
          useValue: mockUserFacade,
        },
        {
          provide: StartupsFacade,
          useValue: mockStartupFacade,
        },
        { provide: ViewContainerRef, useValue: mockViewContainerRef },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentViewDesignerDropdownComponent)
    component = fixture.componentInstance
    fixture.detectChanges()

    // Clear all mocks before each test
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should create the component successfully', (): void => {
    expect(component).toBeTruthy()
  })

  it('should open the dropdown menu when clicked', (): void => {
    // GIVEN the dropdown menu is initially closed
    expect(component.dropdownContentVisibility).toBeFalsy()

    // WHEN the user clicks on the dropdown button
    component.toggleDropdown()

    // THEN the dropdown menu should open
    expect(component.dropdownContentVisibility).toBeTruthy()
  })

  it('should close the dropdown menu when explicitly requested', (): void => {
    // GIVEN the dropdown menu is open
    component.dropdownContentVisibility = true

    // WHEN the user requests to close the dropdown
    component.toggleDropdown(false)

    // THEN the dropdown menu should close
    expect(component.dropdownContentVisibility).toBeFalsy()
  })

  it('should show available views while hiding the currently selected one', (): void => {
    // GIVEN a list of views including the currently selected one
    component.allItems = [
      { viewId: 1, viewName: 'Selected View', allowToLoad: true },
      { viewId: 2, viewName: 'Another View', allowToLoad: true },
      { viewId: 3, viewName: 'Third View', allowToLoad: true },
    ] as ViewModel[]
    component.selectedDocumentViewDesignerDropdownItem.set({
      viewId: 1,
      viewName: 'Selected View',
    } as ViewModel)

    // WHEN loading the view list
    component.loadMoreItems()

    // THEN the list should show all views except the currently selected one
    expect(component.loadedItems()).not.toContainEqual(
      expect.objectContaining({ viewId: 1 })
    )
  })

  it('should close the dropdown menu when clicking outside of it', (): void => {
    // GIVEN the dropdown menu is open
    component.toggleDropdown(true)

    // WHEN the user clicks somewhere outside the dropdown menu
    document.dispatchEvent(new MouseEvent('click'))
    fixture.detectChanges()

    // THEN the dropdown menu should close automatically
    expect(component.dropdownContentVisibility).toBe(false)
  })

  it('should keep the dropdown open when clicking inside of it', (): void => {
    // GIVEN the dropdown menu is open
    component.toggleDropdown(true)
    component.dropdownContentVisibility = true

    // WHEN the user clicks inside the dropdown menu
    const event = { target: {} } as any
    // Mock the anchor element to simulate click inside
    component.anchor = {
      nativeElement: {
        contains: () => true,
      },
    } as any
    component.documentClick(event)

    // THEN the dropdown menu should remain open
    expect(component.dropdownContentVisibility).toBe(true)
  })

  it('should close the dropdown menu when pressing the Escape key', (): void => {
    // GIVEN the dropdown menu is open
    component.toggleDropdown(true)

    // WHEN the user presses the Escape key
    const event = new KeyboardEvent('keydown', { code: 'Escape' })
    component.keydown(event)

    // THEN the dropdown menu should close
    expect(component.dropdownContentVisibility).toBe(false)
  })

  it('should keep the dropdown menu open when pressing other keys', (): void => {
    // GIVEN the dropdown menu is open
    component.toggleDropdown(true)

    // WHEN the user presses a key other than Escape
    const event = new KeyboardEvent('keydown', { code: 'Enter' })
    component.keydown(event)

    // THEN the dropdown menu should remain open
    expect(component.dropdownContentVisibility).toBe(true)
  })

  it('should not show duplicate views in the list', (): void => {
    // GIVEN a list of views that contains duplicates
    component.allItems = [
      { viewId: 2, viewName: 'Same View', allowToLoad: true },
      { viewId: 2, viewName: 'Same View', allowToLoad: true },
      { viewId: 3, viewName: 'Different View', allowToLoad: true },
    ] as ViewModel[]

    // WHEN loading the view list
    component.loadMoreItems()

    // THEN each view should appear only once in the list
    const items = component.loadedItems()
    const uniqueItems = Array.from(new Set(items.map((item) => item.viewId)))
    expect(items.map((c) => c.viewId)).toStrictEqual(uniqueItems)
  })

  it('should load more views when user scrolls to the bottom', (): void => {
    // GIVEN a large list of views
    const largeNumberOfItems = new Array(50).fill(null).map(
      (_, index) =>
        ({
          viewId: index + 1,
          viewName: `View ${index}`,
          allowToLoad: true,
        } as ViewModel)
    )
    component.allItems = [...largeNumberOfItems] as ViewModel[]

    component.selectedDocumentViewDesignerDropdownItem.set({
      viewId: 999,
      viewName: 'Selected View',
    } as ViewModel)
    // Initially no items are loaded
    component.loadedItems.set([])

    // WHEN user scrolls to the bottom of the list
    component.loadMoreItems()

    // THEN it should load the first batch of views (20)
    expect(component.loadedItems()).toHaveLength(20)

    // WHEN user scrolls again to the bottom
    component.loadMoreItems()

    // THEN it should load the next batch of views (40 total)
    expect(component.loadedItems()).toHaveLength(40)

    // AND the currently selected view should not appear in the list
    expect(component.loadedItems()).not.toContainEqual(
      expect.objectContaining({ viewId: 999 })
    )
  })

  it('should only show views that users are allowed to load', (): void => {
    // GIVEN a list of views with different loading permissions
    component.allItems = [
      { viewId: 1, viewName: 'Allowed View 1', allowToLoad: true },
      { viewId: 2, viewName: 'Blocked View', allowToLoad: false },
      { viewId: 3, viewName: 'Allowed View 2', allowToLoad: true },
      { viewId: 4, viewName: 'Default Allowed View' }, // undefined allowToLoad
    ] as ViewModel[]

    // WHEN loading the view list
    component.loadMoreItems()

    // THEN only views with allowToLoad=true should be shown
    // Views with undefined allowToLoad are not shown because the filterByLoadFlag method
    // only includes items where allowToLoad is truthy
    const items = component.loadedItems()
    expect(items).toHaveLength(2)
    expect(items).toContainEqual(expect.objectContaining({ viewId: 1 }))
    expect(items).toContainEqual(expect.objectContaining({ viewId: 3 }))
    expect(items).not.toContainEqual(expect.objectContaining({ viewId: 2 }))
    expect(items).not.toContainEqual(expect.objectContaining({ viewId: 4 }))
  })

  it('should filter views based on search terms entered by user', (): void => {
    // GIVEN a list of views
    component.allItems = [
      { viewId: 1, viewName: 'Apple Pie', allowToLoad: true },
      { viewId: 2, viewName: 'Banana Bread', allowToLoad: true },
      { viewId: 3, viewName: 'Cherry Cake', allowToLoad: true },
    ] as ViewModel[]

    // AND the user enters a search term
    Object.defineProperty(component, 'searchInput', {
      writable: true,
      value: { value: 'an' },
    })

    // WHEN the user performs a search
    component.updateViewBasedOnSearch()

    // THEN only views matching the search term should be shown
    const items = component.loadedItems()
    expect(items).toHaveLength(1)
    expect(items[0].viewName).toBe('Banana Bread')
  })

  it('should show all views when user clicks "View All" button', (): void => {
    // GIVEN a large list of views
    const items = new Array(30).fill(null).map(
      (_, index) =>
        ({
          viewId: index + 1,
          viewName: `View ${index}`,
          allowToLoad: true,
        } as ViewModel)
    )
    component.allItems = [...items] as ViewModel[]
    component.dropdownContentVisibility = true

    // WHEN the user clicks the "View All" button
    component.viewAllViews()

    // THEN the dropdown should close and all views should be loaded
    expect(component.dropdownContentVisibility).toBe(false)
    expect(component.loadedItems()).toHaveLength(30)
  })

  it('should change icon visibility when hovering over view items', (): void => {
    // GIVEN an icon container element
    const element = document.createElement('span')

    // WHEN the user hovers over a view item (mouseenter)
    component.toggleIconContainerOpaque(element, true)

    // THEN the icon should become visible
    expect(element.style.opacity).toBe('1')

    // WHEN the user moves the cursor away (mouseleave)
    component.toggleIconContainerOpaque(element, false)

    // THEN the icon should become hidden
    expect(element.style.opacity).toBe('0')
  })

  it('should select a view when user clicks on it', (): void => {
    // GIVEN a view item
    const item = {
      viewId: 1,
      viewName: 'Test View',
      allowToLoad: true,
    } as ViewModel
    const event = {
      currentTarget: {
        classList: {
          contains: (): boolean => true,
        },
      },
    }

    // WHEN the user clicks on the view to select it
    component.listItemClickAction(
      item,
      'SELECT' as CommonActionTypes,
      event as any
    )

    // THEN that view should become the selected view
    expect(component.selectedDocumentViewDesignerDropdownItem()).toEqual(item)
    // AND the dropdown menu should close
    expect(component.dropdownContentVisibility).toBe(false)
    // AND the appropriate facades should be called
    expect(mockBreadcrumbService.setConditionChecked).toHaveBeenCalledWith(
      'View Search ',
      true
    )
    expect(mockReviewViewFacade.saveUserDefaultView).toHaveBeenCalledWith(
      123,
      item.viewId
    )
  })

  it('should prepare for editing when user clicks the edit icon', (): void => {
    // GIVEN a view item
    const item = {
      viewId: 1,
      viewName: 'Test View',
      allowToLoad: true,
    } as ViewModel
    const event = {
      currentTarget: {
        classList: {
          contains: (): boolean => false, // Not a list item container (i.e., edit icon)
        },
      },
    }

    // WHEN the user clicks the edit icon for a view
    component.listItemClickAction(
      item,
      'EDIT' as CommonActionTypes,
      event as any
    )

    // THEN the dropdown menu should close
    expect(component.dropdownContentVisibility).toBe(false)
    // AND the view details should be fetched
    expect(mockDocumentViewFacade.fetchViewByViewId).toHaveBeenCalledWith(
      item.viewId
    )
  })

  it('should prepare to create a new view when user clicks "Add New View"', (): void => {
    // GIVEN a user interaction event
    const event = {
      currentTarget: {
        classList: {
          contains: (): boolean => false,
        },
      },
    }

    // WHEN the user clicks the "Add New View" button
    component.listItemClickAction(
      undefined,
      'ADD' as CommonActionTypes,
      event as any
    )

    // THEN the dropdown menu should close
    expect(component.dropdownContentVisibility).toBe(false)
    // AND the document view state should be reset
    expect(mockDocumentViewFacade.resetDocumentViewState).toHaveBeenCalledWith(
      'selectedDocumentView'
    )
  })

  it('should handle view selection with complete view data', (): void => {
    // GIVEN a view item with complete data
    const item = {
      viewId: 1,
      viewName: 'Complete View',
      allowToLoad: true,
      allowToEdit: true,
      viewExpression: 'test expression',
    } as ViewModel

    const event = {
      currentTarget: {
        classList: {
          contains: (): boolean => true,
        },
      },
    }

    // WHEN the user selects the view
    component.listItemClickAction(
      item,
      'SELECT' as CommonActionTypes,
      event as any
    )

    // THEN the selected view should be stored with complete data
    expect(
      mockDocumentViewFacade.storeSelectedDocumentView
    ).toHaveBeenCalledWith(item)
    // AND the view expression should be stored
    expect(
      mockReviewViewFacade.storeSelectedViewDefaultExpression
    ).toHaveBeenCalledWith('test expression')
    // AND the view should be marked as manually changed
    // We can't test this directly because isViewManuallyChanged is a signal, not a mock
  })

  it('should handle add action by opening view management dialog', (): void => {
    // Create a new component with user with view management rights
    const newFixture = TestBed.createComponent(
      DocumentViewDesignerDropdownComponent
    )
    const newComponent = newFixture.componentInstance

    // Manually set the startup facade to return true for rights
    newComponent['startupFacade'] = {
      hasGlobalRight$: jest.fn((userRight: any) => of(true)),
    } as any

    // Manually set the user as internal
    newComponent['_userFacade'] = {
      selectCurrentUserDetails$: of({
        userId: 1,
        fullName: 'Test User',
        userName: 'testuser',
        address: '',
        email: '<EMAIL>',
        phone: '',
        mobile: '',
        fax: '',
        globalRoleId: 1,
        isUserLocked: false,
        isUserDeactivated: false,
        failedLoginAttempts: 0,
        userLockValidUpto: new Date(),
        userLockType: '',
        forceUserToChangePassword: false,
        hasDesktopAccess: true,
        hasWebECAAccess: true,
        hasReviewAccess: true,
        hasTouchAccess: true,
        isADUser: false,
        adUserSID: '',
        adUserGUID: '',
        hasOnDemandAccess: true,
        clientId: 1,
        clientName: 'Test Client',
        showNotification: true,
        isUserApproved: true,
        createdByName: 'Admin',
        createdDate: new Date(),
        userRole: 'internal',
        globalRoleName: 'User',
        isUserAdmin: false,
        userCaseAssignmentModel: '',
        eulaAcceptance: true,
        userlayoutId: 1,
        passwordExpired: false,
        activeSessionId: 1,
        disablePasswordReset: false,
      }),
    } as any

    newFixture.detectChanges()

    // AND an add action event
    const event = {
      currentTarget: {
        classList: {
          contains: (): boolean => false, // Not a list item container
        },
      },
    }

    // WHEN the user clicks add new view
    newComponent.listItemClickAction(
      undefined,
      'ADD' as CommonActionTypes,
      event as any
    )

    // THEN the selected document view state should be reset
    expect(mockDocumentViewFacade.resetDocumentViewState).toHaveBeenCalledWith(
      'selectedDocumentView'
    )
  })
})
